import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  showErrorToast,
  showSuccessToast,
  handleApiError,
} from '@/utils/toast';
import { Card } from '@/components/ui/card';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { useResendOTPMutation } from '@/store/api/apiSlice';
import bg from '../../public/Images/bg4.png';

const otpSchema = z.object({
  confirmationCode: z
    .string()
    .length(6, 'OTP must be 6 digits')
    .regex(/^\d+$/, 'OTP must contain only numbers'),
});

const VerifyOTP = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [confirmationCode, setOtp] = useState('');
  const [resendOtp, { isLoading }] = useResendOTPMutation();
  const [timer, setTimer] = useState(30);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const phone_number = location.state?.phone_number;

  useEffect(() => {
    if (!phone_number) {
      showErrorToast(
        'Phone number not found. Please try the forgot password process again.'
      );
      navigate('/forgot-password');
      return;
    }
    startTimer();
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [phone_number, navigate]);

  const startTimer = () => {
    setTimer(30);
    if (timerRef.current) clearInterval(timerRef.current);

    timerRef.current = setInterval(() => {
      setTimer(prevTimer => {
        if (prevTimer <= 1) {
          clearInterval(timerRef.current!);
          return 0;
        }
        return prevTimer - 1;
      });
    }, 1000);
  };

  const handleChange = (value: string) => {
    setErrors(prev => ({ ...prev, otp: '' }));
    setOtp(value);
  };

  const handleVerify = (e: React.FormEvent) => {
    e.preventDefault();

    const validationResult = otpSchema.safeParse({ confirmationCode });

    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach(err => {
        fieldErrors[err.path[0]] = err.message;
      });
      setErrors(fieldErrors);
      return;
    }

    setTimeout(() => {
      showSuccessToast('OTP received successfully');
      navigate('/reset-password', {
        state: { phone_number, confirmationCode },
      });
    }, 1500);
  };

  const handleResend = async (e: React.FormEvent) => {
    e.preventDefault();
    if (timer > 0) return;
    try {
      const response = await resendOtp({ phone_number }).unwrap();
      if (response) {
        showSuccessToast('New OTP has been sent to your mobile number');
        startTimer();
      }
    } catch (error: unknown) {
      console.error('Resend OTP error:', error);

      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data
          ?.message || (error as { message?: string })?.message;

      const statusCode = (error as { status?: number })?.status;
      if (statusCode === 404) {
        showErrorToast('Phone number not found. Please check and try again.');
      } else if (statusCode === 429) {
        showErrorToast(
          'Too many requests. Please wait before requesting another OTP.'
        );
      } else {
        handleApiError(error, {
          [statusCode]: errorMessage || 'Failed to send OTP. Please try again.',
        });
      }
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins < 10 ? '0' : ''}${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <div className='relative h-screen w-full overflow-hidden'>
      {}
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src={bg}
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      {}
      <div className='absolute inset-0 bg-black bg-opacity-30' />
      <div className=' relative z-20 flex-1 flex flex-col items-center justify-center px-5 pt-20 md:pt-32'>
        <Link to='/'>
          <img
            src='../../public/Images/Logo.svg'
            alt='Nurses team'
            className='w-[200px] h-[60px] object-cover bg-transparent animate-fade-in max-w-md mx-auto mb-4 '
          />
        </Link>

        <Card className='w-full max-w-md bg-white rounded-md p-6 shadow-lg'>
          <h2 className='text-2xl font-bold text-center mb-6 text-nursery-blue'>
            Verify OTP
          </h2>

          <p className='text-center text-gray-800 mb-6'>
            Please enter the 6-digit OTP sent to your mobile number.
          </p>

          <form onSubmit={handleVerify} className='space-y-4'>
            <div>
              <Input
                id='otp'
                type='text'
                placeholder='Enter 6-digit OTP'
                value={confirmationCode}
                onChange={e => handleChange(e.target.value)}
                className='w-full h-11 text-center text-base tracking-widest transition-all'
                maxLength={6}
              />
              {errors.confirmationCode && (
                <p className='text-red-500 text-sm text-center'>
                  {errors.confirmationCode}
                </p>
              )}
            </div>

            <Button
              type='submit'
              className='w-full h-10 bg-[#4AB4CE] text-white'
              disabled={isLoading || confirmationCode.length !== 6}
            >
              {isLoading ? 'Verifying...' : 'Verify OTP'}
            </Button>
            <Button
              type='button'
              variant='outline'
              onClick={() => navigate('/login')}
              className='w-full h-10 border-[#4AB4CE] text-[#4AB4CE]'
            >
              Cancel
            </Button>
          </form>

          <div className='mt-6 text-center'>
            <button
              onClick={handleResend}
              disabled={timer > 0}
              className={`text-base ${timer > 0 ? 'text-gray-500' : 'text-nursery-blue font-medium'}`}
            >
              {timer > 0
                ? `Resend verification code after ${formatTime(timer)}`
                : 'Resend verification code'}
            </button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default VerifyOTP;
