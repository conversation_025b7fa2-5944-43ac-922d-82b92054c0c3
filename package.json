{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lint:remove-comments": "eslint . --ext .js,.jsx,.ts,.tsx --fix --config eslint.remove-comments.config.js", "format": "prettier --write .", "format:check": "prettier --check .", "format:fix": "prettier --write . && npm run lint:fix", "prepare": "husky"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^3.10.0", "@mui/material": "^7.2.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.6.1", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "google-maps": "^4.3.3", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "lucide-vue-next": "^0.514.0", "motion": "^12.19.1", "next-themes": "^0.3.0", "react": "^18.3.1", "react-datepicker": "^8.2.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.30.0", "recharts": "^2.12.7", "redux-persist": "^6.0.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.31.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-no-comments": "^1.1.10", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-unused-imports": "^4.1.4", "glob": "^11.0.3", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "prettier": "^3.6.2", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.37.0", "vite": "^5.4.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md,html}": ["prettier --write"]}}