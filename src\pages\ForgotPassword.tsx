import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Logo from '../components/Logo';
import { useForgotPasswordMutation } from '@/store/api/apiSlice';
import {
  showSuccessToast,
  showErrorToast,
  handleApiError,
  COMMON_SUCCESS_MESSAGES,
} from '@/utils/toast';
import bg from '../../public/Images/bg4.png';
import ResponsiveLoader from '@/components/Loader';

const ForgotPassword = () => {
  const navigate = useNavigate();
  const [phone_number, setPhoneNumber] = useState('');
  const [Forgot, { isLoading }] = useForgotPasswordMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isLoading) {
      return (
        <div className='flex items-center justify-center p-4'>
          <ResponsiveLoader />
        </div>
      );
    }

    try {
      const response = await Forgot({ phone_number }).unwrap();
      if (response) {
        setTimeout(() => {
          showSuccessToast(COMMON_SUCCESS_MESSAGES.OTP_SENT);
          navigate('/otp-verify', { state: { phone_number } });
        }, 1500);
      }
    } catch (error) {
      console.error('Forgot password error:', error);

      const errorMessage = error?.data?.error || error?.message;
      const statusCode = error?.status;

      switch (statusCode) {
        case 404:
          showErrorToast('User not found. Please check your phone number.');
          break;
        case 400:
          if (errorMessage?.includes('Phone number is required')) {
            showErrorToast('Phone number is required. Please try again.');
          } else if (errorMessage?.includes('Invalid phone number format')) {
            showErrorToast(
              'Invalid phone number format. Please check and try again.'
            );
          } else {
            showErrorToast(
              errorMessage || 'Invalid request. Please check your details.'
            );
          }
          break;
        default:
          handleApiError(error, {
            [statusCode]:
              errorMessage || 'Failed to send OTP. Please try again.',
          });
      }
    }
  };

  const handleMobileNumber = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    if (!value.startsWith('+91')) {
      value = '+91' + value.replace(/^\+91/, '');
    }
    setPhoneNumber(value);
  };

  return (
    <div className='relative w-full overflow-hidden min-h-screen flex flex-col'>
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src={bg}
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

      <div className='relative z-10 h-full w-full flex-1 flex flex-col items-center justify-center pt-10 px-6 gap-3'>
        <Logo />

        {}
        <div className='w-full max-w-md bg-white rounded-xl p-6 shadow-lg'>
          <h2 className='text-2xl font-bold mb-6 text-nursery-blue'>
            Forgot Password
          </h2>

          <form onSubmit={handleSubmit} className='space-y-4'>
            <Input
              type='text'
              placeholder='Enter Mobile Number'
              value={phone_number}
              onChange={handleMobileNumber}
              className='w-full h-11 text-base'
              required
            />

            <Button
              type='submit'
              className='w-full h-11 bg-[#5EB2CC] hover:bg-[#4996B5] text-white text-lg font-medium'
              disabled={isLoading}
            >
              Send OTP
            </Button>
            <Button
              type='button'
              variant='outline'
              onClick={() => navigate('/login')}
              className='w-full h-10 border-[#4AB4CE] text-[#4AB4CE]'
            >
              Cancel
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
