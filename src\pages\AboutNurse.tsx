import React, { useState } from 'react';
import {
  User,
  Star,
  MapPin,
  IndianRupee,
  Check,
  CircleCheck,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const NurseCard = ({ nurse }) => {
  const navigate = useNavigate();
  const [expanded, setExpanded] = useState(false);
  const [showVerifiedTitle, setShowVerifiedTitle] = useState(false);
  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  const getRatingColor = (rating: number) => {
    if (rating <= 1) return 'text-red-500';
    if (rating <= 3) return 'text-orange-400';
    if (rating <= 5) return 'text-green-600';
    return 'text-gray-600';
  };

  return (
    <div
      className={`bg-[#F2F2F2] md:p-3  p-0 flex flex-col rounded-xl transition-all duration-500 ease-in-out ${
        expanded ? 'mb-8 mt-4' : ' '
      } ${expanded ? 'md:w-full' : ''}`}
    >
      <div className='flex flex-col md:flex-row md:justify-between w-full'>
        <div className='flex flex-row md:items-center items-start gap-3'>
          <span className='p-2 bg-nursery-blue rounded-full'>
            <User className='text-gray-100' size={26} />
          </span>
          <div className='items-center w-full'>
            <div className='flex flex-row justify-between items-center gap-4 w-full'>
              <div className='flex flex-row items-center gap-2'>
                <p className='text-base font-bold text-black'>{nurse.name}</p>
                <button
                  title='Nurse Verified'
                  onClick={() => {
                    setShowVerifiedTitle(true);
                    setTimeout(() => setShowVerifiedTitle(false), 2000);
                  }}
                  className='relative'
                >
                  <CircleCheck className='w-5 h-5 text-[#00A912]' />
                  {showVerifiedTitle && (
                    <span className='absolute left-1/2 top-full -translate-x-1/2 mt-2 px-2 py-1 bg-nursery-blue text-white text-xs rounded shadow z-10 whitespace-nowrap'>
                      Verified Nurse
                    </span>
                  )}
                </button>
              </div>
              <span className='flex items-center gap-1 text-base font-medium text-black'>
                <Star
                  size={16}
                  strokeWidth={2.5}
                  className={`mr-1 ${getRatingColor(nurse.rating)}`}
                />
                {nurse.rating}
              </span>
            </div>
            <div className='flex flex-row items-center gap-6 mt-1'>
              <p className='flex md:items-center items-start gap-1'>
                <MapPin
                  className='md:w-4 md:h-4 w-5 h-5 items-center text-nursery-darkBlue'
                  opacity='75%'
                />
                <span className='text-slate-700 text-base'>
                  {nurse.location !== 'Location not available'
                    ? nurse.location.split('-').slice(0, 1).join(',')
                    : nurse.location}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div className='flex sm:flex-row md:gap-4 gap-4 flex-col items-center mt-4 sm:mt-0 md:ml-6'>
          <p className='flex items-center text-nursery-darkBlue font-medium px-[10px] py-[6px]  '>
            Fees: <IndianRupee size={16} />
            {nurse.fees} / Hour
          </p>
          <div className='flex flex-row items-center gap-2 transition-all duration-300 ease-linear'>
            <p
              className='bg-nursery-blue  hover:bg-nursery-darkBlue hover:bg-opacity-85 text-white py-1 px-3 rounded-md transition-colors duration-200 ease-in-out cursor-pointer'
              onClick={toggleExpand}
            >
              About Nurse
            </p>
            {}
            <p
              className='bg-nursery-blue  hover:bg-nursery-darkBlue hover:bg-opacity-85 text-white py-1 px-3 rounded-md transition-colors duration-200 ease-in-out cursor-pointer'
              onClick={() => {
                navigate('/view-more', { state: { nurse } });
              }}
            >
              View More
            </p>
          </div>
        </div>
      </div>
      {}
      {expanded && (
        <div className='mt-4 border-t border-slate-400 pt-4 animate-fadeIn transition-all duration-700 ease-in-out'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4 place-items-center '>
            <div className='md:col-span-1 cursor-pointer '>
              <h3 className='font-semibold text-lg mb-2 text-center items-center '>
                Address{' '}
              </h3>
              <div className='flex flex-row items-center gap-6 mt-1'>
                <p className='flex md:items-center items-start gap-2'>
                  <MapPin
                    className='md:w-6 md:h-6 w-12 h-12 items-center text-nursery-darkBlue'
                    opacity='75%'
                  />
                  <span className='text-slate-700 text-base'>
                    {nurse.location}
                  </span>
                </p>
              </div>
            </div>
            <div className='md:col-span-1 cursor-pointer '>
              <h3 className='font-semibold text-lg mb-2 text-center '>
                Services
              </h3>
              <ul className='grid grid-cols-1 md:grid-cols-2 gap-2 '>
                {nurse.services.map((service, index) => (
                  <li key={index} className='flex items-center gap-3'>
                    <Check
                      strokeWidth={3}
                      className='w-5 h-6 rounded-full text-nursery-blue '
                    />
                    {service}
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className='flex md:flex-row flex-col gap-3 justify-between mt-6 pt-4 border-t border-slate-400 text-center'>
            <button
              onClick={() => {
                navigate('/book-slot', { state: { nurse } });
              }}
              className='bg-nursery-blue hover:bg-nursery-darkBlue text-white py-2 px-4 rounded-md transition-colors duration-200 ease-in-out'
            >
              Book Appointment
            </button>
            <button
              onClick={toggleExpand}
              className='bg-gray-400 px-4 hover:bg-nursery-blue bg-opacity-60 hover:text-gray-100 text-gray-700 py-2 rounded-md transition-colors duration-200 ease-in-out'
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NurseCard;
