import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Eye, EyeOff, Check, X } from 'lucide-react';
import {
  showSuccessToast,
  showErrorToast,
  handleApiError,
  COMMON_SUCCESS_MESSAGES,
} from '@/utils/toast';
import { z } from 'zod';
import { useConfirmForgotPasswordMutation } from '@/store/api/apiSlice';

const passwordSchema = z
  .object({
    newPassword: z
      .string()
      .regex(
        /[^A-Za-z0-9]/,
        'Password must contain at least one special character'
      )
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/[0-9]/, 'Password must contain at least one number')
      .min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

const ResetPassword = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [resetPassword, { isLoading }] = useConfirmForgotPasswordMutation();
  const [errors, setErrors] = useState({});

  const phone_number = location.state?.phone_number;
  const confirmationCode = location.state?.confirmationCode;

  const handleInputChange = (field, value) => {
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    switch (field) {
      case 'newPassword':
        setNewPassword(value);
        break;
      case 'confirmPassword':
        setConfirmPassword(value);
        break;
      default:
        break;
    }
  };

  const _validatePassword = password => {
    const requirements = [
      { test: password.length >= 8, text: 'At least 8 characters' },
      { test: /[A-Z]/.test(password), text: 'One uppercase letter' },
      { test: /[0-9]/.test(password), text: 'One number' },
      { test: /[^A-Za-z0-9]/.test(password), text: 'One special character' },
    ];
    return requirements;
  };

  const handleSubmit = async e => {
    e.preventDefault();
    setErrors({});

    const formData = {
      newPassword,
      confirmPassword,
    };
    const validationResult = passwordSchema.safeParse(formData);

    if (!validationResult.success) {
      const fieldErrors = {};
      validationResult.error.errors.forEach(err => {
        fieldErrors[err.path[0]] = err.message;
      });
      setErrors(fieldErrors);
      return;
    }

    try {
      const response = await resetPassword({
        phone_number,
        confirmationCode,
        newPassword,
      }).unwrap();

      if (response) {
        setTimeout(() => {
          showSuccessToast(COMMON_SUCCESS_MESSAGES.PASSWORD_RESET_SUCCESS);
          navigate('/login');
        }, 1500);
      } else {
        showErrorToast('Password reset failed');
      }
    } catch (error) {
      console.error('Password reset error:', error);

      const errorMessage = error?.data?.error || error?.message;
      const statusCode = error?.status;

      switch (statusCode) {
        case 404:
          showErrorToast('User not found. Please check your phone number.');
          break;
        case 400:
          if (errorMessage?.includes('confirmation code')) {
            showErrorToast(
              'Invalid or expired confirmation code. Please request a new OTP.'
            );
          } else if (errorMessage?.includes('password format')) {
            showErrorToast(
              'Password does not meet requirements. Please check the criteria.'
            );
          } else {
            showErrorToast(
              errorMessage || 'Invalid request. Please check your details.'
            );
          }
          break;
        default:
          handleApiError(error, {
            [statusCode]:
              errorMessage || 'Password reset failed. Please try again.',
          });
      }
    }
  };

  return (
    <div className='relative h-screen w-full overflow-hidden'>
      {}
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='../../public/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      {}
      <div className='absolute inset-0 bg-black bg-opacity-30' />

      <div className='relative z-20 flex-1 flex flex-col items-center justify-center px-4 pt-10 md:pt-20'>
        <img
          src='../../public/Images/Logo.svg'
          alt='Nurses team'
          className='w-[200px] h-[60px] object-cover bg-transparent animate-fade-in max-w-md mb-2'
        />

        {}
        <div className='w-full max-w-md bg-white rounded-xl p-4 shadow-lg'>
          <h2 className='text-2xl font-bold text-nursery-darkBlue mb-6'>
            Password Recovery
          </h2>

          <form onSubmit={handleSubmit} noValidate className='space-y-6'>
            {}
            <div className='relative'>
              <p className='text-sm text-gray-800 mb-1'>New Password</p>
              <div className='relative'>
                <Input
                  type={showNewPassword ? 'text' : 'password'}
                  value={newPassword}
                  onChange={e =>
                    handleInputChange('newPassword', e.target.value)
                  }
                  className='w-full h-11 text-base pr-10'
                  required
                />
                <button
                  type='button'
                  className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600'
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? <Eye size={20} /> : <EyeOff size={20} />}
                </button>
              </div>
              {errors.newPassword && (
                <p className='text-red-500 text-sm mt-1'>
                  {errors.newPassword}
                </p>
              )}
            </div>

            {}
            <div className='relative'>
              <p className='text-sm text-gray-800 mb-1'>Re-type Password</p>
              <div className='relative'>
                <Input
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={e =>
                    handleInputChange('confirmPassword', e.target.value)
                  }
                  className='w-full h-11 text-base pr-10'
                  required
                />
                <button
                  type='button'
                  className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600'
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <Eye size={20} />
                  ) : (
                    <EyeOff size={20} />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className='text-red-500 text-sm mt-1'>
                  {errors.confirmPassword}
                </p>
              )}

              {}
              {newPassword && confirmPassword && (
                <div className='flex items-center text-xs mt-2'>
                  {newPassword === confirmPassword ? (
                    <>
                      <Check className='h-3 w-3 text-green-500 mr-1' />
                      <span className='text-green-700'>Passwords match</span>
                    </>
                  ) : (
                    <>
                      <X className='h-3 w-3 text-red-500 mr-1' />
                      <span className='text-red-700'>
                        Passwords don&apos;t match
                      </span>
                    </>
                  )}
                </div>
              )}
            </div>

            <Button
              type='submit'
              className='w-full h-11 bg-[#5EB2CC] hover:bg-[#4996B5] text-white text-lg font-medium'
              disabled={isLoading}
            >
              {isLoading ? 'Resetting Password...' : 'Reset Password'}
            </Button>
          </form>
        </div>

        {}
        <div className='mt-6 w-full max-w-md p-4 bg-white bg-opacity-90 rounded-lg border border-gray-200'>
          <h3 className='text-sm font-medium text-gray-800 mb-2'>
            Security Tips:
          </h3>
          <ul className='text-xs text-gray-700 space-y-1'>
            <li>
              • Password must be at least 8 characters and include at least one
              special character, one uppercase letter, and one number.
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
