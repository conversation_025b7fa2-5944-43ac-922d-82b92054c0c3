import { FlatCompat } from "@eslint/eslintrc";
import js from "@eslint/js";
import path from "path";
import { fileURLToPath } from "url";
import tseslint from "typescript-eslint";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
});

// Common ignore patterns
const baseIgnores = [
  "**/node_modules/**",
  "dist/**",
  "build/**",
  ".git/**",
  "tailwind.config.ts",
  "vite.config.ts",
  "src/types/google-maps.d.ts",
];

// Common React settings
const reactSettings = {
  react: {
    version: "detect",
  },
};

// Common unused imports rules
const unusedImportsRules = {
  "unused-imports/no-unused-imports": "error",
  "unused-imports/no-unused-vars": [
    "warn",
    {
      vars: "all",
      varsIgnorePattern: "^_",
      args: "after-used",
      argsIgnorePattern: "^_",
    },
  ],
};

// Common React rules
const commonReactRules = {
  "react/react-in-jsx-scope": "off",
  "react/prop-types": "off",
  "react/no-unknown-property": "off",
};

// Common TypeScript rules
const commonTypeScriptRules = {
  "no-unused-vars": "off",
  "@typescript-eslint/no-unused-vars": "off",
  "@typescript-eslint/explicit-function-return-type": "off",
  "@typescript-eslint/explicit-module-boundary-types": "off",
  "@typescript-eslint/no-explicit-any": "warn",
};

// TypeScript language options
const typeScriptLanguageOptions = {
  parser: tseslint.parser,
  parserOptions: {
    project: "./tsconfig.app.json",
    ecmaVersion: "latest",
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
};

/**
 * Creates ESLint configuration with customizable options
 * @param {Object} options - Configuration options
 * @param {boolean} options.allowComments - Whether to allow comments (default: true)
 * @param {boolean} options.includeSpacedCommentRule - Whether to include spaced-comment rule (default: false)
 * @param {string[]} options.additionalIgnores - Additional ignore patterns (default: [])
 * @param {string[]} options.additionalPlugins - Additional plugins (default: [])
 * @param {Object} options.additionalRules - Additional rules (default: {})
 * @returns {Array} ESLint configuration array
 */
export function createEslintConfig(options = {}) {
  const {
    allowComments = true,
    includeSpacedCommentRule = false,
    additionalIgnores = [],
    additionalPlugins = [],
    additionalRules = {},
  } = options;

  // Build plugins array
  const plugins = ["react", "react-hooks", "unused-imports", ...additionalPlugins];
  if (!allowComments) {
    plugins.push("no-comments");
  }

  // Build ignore patterns
  const ignores = [...baseIgnores, ...additionalIgnores];

  // Build TypeScript rules
  const typeScriptRules = {
    ...tseslint.configs.recommendedTypeChecked.rules,
    ...unusedImportsRules,
    ...commonReactRules,
    ...commonTypeScriptRules,
    ...additionalRules,
  };

  // Add spaced-comment rule if requested
  if (includeSpacedCommentRule) {
    typeScriptRules["spaced-comment"] = [
      "error",
      "always",
      {
        line: {
          markers: ["/"],
          exceptions: ["-", "+"],
        },
        block: {
          markers: ["*"],
          exceptions: ["*"],
          balanced: true,
        },
      },
    ];
  }

  // Add no-comments rule if comments are not allowed
  if (!allowComments) {
    typeScriptRules["no-comments/disallowComments"] = "error";
  }

  // Build JavaScript rules
  const javaScriptRules = {
    ...unusedImportsRules,
    ...commonReactRules,
    ...additionalRules,
  };

  if (!allowComments) {
    javaScriptRules["no-comments/disallowComments"] = "error";
  }

  return [
    // Ignore patterns
    {
      ignores,
    },

    // Base configurations
    js.configs.recommended,
    ...tseslint.configs.recommended,

    // React and other plugin configurations
    ...compat.config({
      extends: [
        "plugin:react/recommended",
        "plugin:react-hooks/recommended",
        "plugin:prettier/recommended",
      ],
      plugins,
    }),

    // Config files specific rules
    {
      files: ["*.config.js", ".eslintrc.js"],
      rules: {
        "@typescript-eslint/await-thenable": "off",
        "@typescript-eslint/no-floating-promises": "off",
        "@typescript-eslint/no-misused-promises": "off",
        ...Object.fromEntries(
          Object.keys(tseslint.configs.recommendedTypeChecked.rules || {}).map(
            (key) => [key, "off"]
          )
        ),
      },
    },

    // TypeScript files
    {
      files: ["**/*.ts", "**/*.tsx"],
      languageOptions: typeScriptLanguageOptions,
      rules: typeScriptRules,
      settings: reactSettings,
    },

    // JavaScript files
    {
      files: ["**/*.js", "**/*.jsx"],
      rules: javaScriptRules,
      settings: reactSettings,
    },
  ];
}
