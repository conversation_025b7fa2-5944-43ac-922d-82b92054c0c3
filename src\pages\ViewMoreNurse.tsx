import React, { useEffect, useState } from 'react';
import {
  MapPin,
  Calendar,
  Star,
  ChevronDown,
  ArrowLeft,
  User,
  IndianRupee,
  Check,
  ChevronUp,
} from 'lucide-react';
import bg from '../../public/Images/bg4.png';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useLocation, useNavigate } from 'react-router-dom';
import FeedbackCarousel from '@/components/FeedbackCarousel';
import { useGetFeedbackQuery } from '@/store/api/nurseApiSlice';
import Stars from '../../public/Images/stars.svg';
import { showErrorToast } from '@/utils/toast';
import { Button } from '@/components/ui/button';
import Footer from '@/components/Footer';

const HealthcareProfile: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const nurse = location.state?.nurse;
  const nurseId = nurse.nurse_id;
  const [showFullAbout, setShowFullAbout] = useState(false);
  const [feedbackRetryAttempts, _setFeedbackRetryAttempts] = useState(0);

  const getRatingColor = (rating: number) => {
    if (rating <= 1) return 'text-red-500';
    if (rating <= 3) return 'text-orange-400';
    if (rating <= 5) return 'text-green-600';
    return 'text-gray-600';
  };

  const {
    data: feedbackData,
    isLoading: feedbackLoading,
    error: feedbackError,
  } = useGetFeedbackQuery(nurseId, {
    skip: !nurseId,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
  });

  useEffect(() => {
    if (feedbackError) {
      console.error('Feedback fetch error:', feedbackError);

      if (feedbackRetryAttempts < 3) {
        showErrorToast(
          `Failed to load feedback data (Attempt ${feedbackRetryAttempts + 1})`
        );
      }
    }
  }, [feedbackError, feedbackRetryAttempts]);

  const getFeedbacks = () => {
    if (feedbackData?.feedback && Array.isArray(feedbackData.feedback)) {
      return feedbackData.feedback;
    }
    if (Array.isArray(feedbackData)) {
      return feedbackData;
    }
    return [];
  };

  const feedbacks = getFeedbacks();
  const totalFeedbacks = feedbacks.length;

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      <header className='relative w-full overflow-hidden text-white pb-10 flex flex-col'>
        <div className='absolute inset-0 w-full h-[220px] z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className=' object-cover w-full '
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        {}
        <div className='flex justify-center items-center'>
          <div className='absolute top-12 left-5 z-10 items-center'>
            <button onClick={() => navigate(-1)} className='mr-4'>
              <ArrowLeft className='h-6 w-6' />
            </button>
          </div>
          {}
          <div className='mt-6 mb-2'>
            <div className='relative'>
              <Avatar className='h-20 w-20 bg-white text-nursery-blue'>
                <AvatarFallback className='bg-white text-nursery-blue text-2xl'>
                  <User className='h-12 w-12' />
                </AvatarFallback>
              </Avatar>
            </div>
          </div>
        </div>
      </header>

      {}
      <div className='mx-auto w-11/12 md:10/12 -mt-8 relative z-10'>
        <div className='bg-[#F2F2F2] rounded-lg shadow-sm p-6 md:p-8 mb-20 md:mb-5'>
          <div className='flex justify-between items-start mb-3'>
            <h1 className='text-xl font-semibold text-nursery-darkBlue'>
              {nurse.name}
            </h1>
            <div className='bg-nursery-blue flex justify-center items-center text-white px-3 py-1 rounded-full text-sm '>
              <IndianRupee size={14} className='items-center text-center' />
              {nurse.fees} / Hour
            </div>
          </div>

          <div className='flex items-center text-gray-900 gap-2 mb-4'>
            <MapPin
              className='md:w-5 md:h-5 w-10 h-10 items-center text-nursery-darkBlue'
              opacity='75%'
            />
            <span className='text-sm'>{nurse.location}</span>
          </div>

          {}
          <div className='flex justify-around items-center mb-6'>
            {}

            <div className='text-center'>
              <div className='flex items-center justify-center mb-1'>
                <Calendar size={16} className='text-nursery-blue mr-1' />
                <span className='font-semibold text-gray-900'>
                  {nurse.total_years_of_exp}+ Years
                </span>
              </div>
              <span className='text-sm text-gray-900'>Experience</span>
            </div>

            <div className='text-center'>
              <div className='flex items-center justify-center mb-1'>
                <Star
                  size={16}
                  strokeWidth={2.5}
                  className={`mr-1 ${getRatingColor(nurse.rating)}`}
                />
                <span className='font-semibold text-gray-900'>
                  {nurse.rating}
                </span>
              </div>
              <span className='text-sm text-gray-900'>Ratings</span>
            </div>
          </div>

          {}
          <div className='mb-5'>
            <h3 className='font-semibold text-nursery-darkBlue mb-3'>
              Nurse Unique ID -{' '}
              <a
                href='https://nrts.indiannursingcouncil.gov.in/login.nic'
                target='_blank'
                className='text-md font-semibold text-nursery-blue underline'
                rel='noreferrer'
              >
                {nurse.nuid}
              </a>
            </h3>
          </div>

          {}
          <div className='mb-6'>
            <h3 className='font-semibold text-nursery-darkBlue mb-3'>
              Services offered
            </h3>
            <ul className='grid grid-cols-1 md:grid-cols-2 gap-2 '>
              {nurse.services.map((service, index) => (
                <li key={index} className='flex items-center gap-3'>
                  <Check
                    strokeWidth={3}
                    className='w-5 h-6 rounded-full text-nursery-blue '
                  />
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {}
          <div className='mb-6'>
            <h3 className='font-semibold text-nursery-darkBlue mb-3'>About</h3>
            {nurse.about ? (
              <>
                <p
                  className={`text-gray-900 text-sm leading-relaxed mb-2 transition-all duration-300 ${
                    showFullAbout ? '' : 'line-clamp-2'
                  }`}
                >
                  {nurse.about}
                </p>
                {}
                <button
                  onClick={() => setShowFullAbout(!showFullAbout)}
                  className='text-nursery-blue text-sm font-medium flex items-center md:hidden '
                >
                  {showFullAbout ? 'View less' : 'View more'}
                  {showFullAbout ? (
                    <ChevronUp size={16} className='ml-1' />
                  ) : (
                    <ChevronDown size={16} className='ml-1' />
                  )}
                </button>
              </>
            ) : (
              <p className='text-gray-900 text-sm'>No description available.</p>
            )}
          </div>

          {}
          {}
          <div className='md:pb-3'>
            <div className='flex flex-row items-center justify-between pb-6'>
              <h3 className='font-semibold text-nursery-darkBlue'>
                Reviews ({totalFeedbacks})
              </h3>
            </div>

            {}
            {feedbackLoading ? (
              <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2 min-h-[280px]'>
                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-nursery-darkBlue'></div>
                <p className='text-base font-medium text-slate-500'>
                  Loading feedbacks...
                </p>
              </div>
            ) : feedbackError ? (
              <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2 min-h-[280px]'>
                <img
                  src={Stars}
                  alt='Error loading feedbacks'
                  className='p-2 w-20'
                />
                <p className='text-base font-medium text-red-500'>
                  Failed to load feedbacks
                </p>
                <p className='text-xs text-gray-900 mb-2'>
                  Attempts: {feedbackRetryAttempts + 1}/3
                </p>
              </div>
            ) : !nurseId ? (
              <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2 min-h-[280px]'>
                <img src={Stars} alt='No User ID' className='p-2 w-20' />
                <p className='text-base font-medium text-orange-500'>
                  Unable to load feedbacks
                </p>
                <p className='text-xs text-gray-400'>User ID not available</p>
              </div>
            ) : (
              <FeedbackCarousel feedbacks={feedbacks} />
            )}
          </div>
          {}
          <div className='mt-5'>
            <div>
              <Button
                className='md:w-1/4 w-full mx-auto flex justify-center  bg-nursery-blue text-white py-3 rounded-lg font-semibold text-lg hover:bg-nursery-blue hover:opacity-85 transition-colors duration-200'
                onClick={() => navigate('/book-slot', { state: { nurse } })}
              >
                Book
              </Button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default HealthcareProfile;
